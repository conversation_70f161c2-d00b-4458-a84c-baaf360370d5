[{"__type__": "cc.Prefab", "_name": "GameOver", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "GameOver", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 184}, {"__id__": 192}, {"__id__": 200}, {"__id__": 208}, {"__id__": 216}, {"__id__": 224}], "_active": true, "_components": [{"__id__": 232}, {"__id__": 234}], "_prefab": {"__id__": 236}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "gameOver", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 13}, {"__id__": 21}, {"__id__": 49}, {"__id__": 77}, {"__id__": 105}, {"__id__": 113}, {"__id__": 121}, {"__id__": 129}, {"__id__": 137}, {"__id__": 145}, {"__id__": 153}, {"__id__": 161}, {"__id__": 169}], "_active": true, "_components": [{"__id__": 177}, {"__id__": 179}, {"__id__": 181}], "_prefab": {"__id__": 183}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3drqU8YYlHJpfeSZ1twV6H"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03AYpC4gRNoI9x3lbOeSU3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_opacity": 150}, {"__type__": "cc.CompPrefabInfo", "fileId": "77J1B7GiBBII7cJMUqjFpl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 11}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0amxHtJr5PTYxSsPHkHQlR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2/I3diKhMjaVI2ai9poIv"}, {"__type__": "cc.Node", "_name": "ImageView_642", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}], "_prefab": {"__id__": 20}, "_lpos": {"__type__": "cc.Vec3", "x": 320, "y": 480, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.812, "y": 0.812, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b270e6e2-9bde-42fd-bc9d-8196e5daa84b@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1/KEkViZEcbWsuhsRslkX"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5fz1pcIRCC59fL9tB4uUT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 711, "height": 701}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aL0yiBrtC15bjaqUNYrkU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2gtZgHoFHFIXu36/1rw63"}, {"__type__": "cc.Node", "_name": "Button_643", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 22}, {"__id__": 30}], "_active": true, "_components": [{"__id__": 38}, {"__id__": 40}, {"__id__": 42}, {"__id__": 44}, {"__id__": 46}], "_prefab": {"__id__": 48}, "_lpos": {"__type__": "cc.Vec3", "x": 321, "y": 346, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "ImageView_646", "_objFlags": 0, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 23}, {"__id__": 25}, {"__id__": 27}], "_prefab": {"__id__": 29}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -0.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 24}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "22ba6b73-**************-a18123323615@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59C4YrXh5EeKvrsKlORtE+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 26}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7xGu9QsBKO5X30XmyaBTA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "__prefab": {"__id__": 28}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 304, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aBU1/WiBIDoF4z1iLBx+Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "93d8wekmJFg5KfKws+WIYf"}, {"__type__": "cc.Node", "_name": "ImageView_647", "_objFlags": 0, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 33}, {"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -0.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 32}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ce9964d6-9cf7-4714-b71d-f0a78161a571@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0o3W7aOJBoaLtv8W4cNyN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 34}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bPQAitRxHbphcTeBX7Lov"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 36}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 165, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0mHMQGsZOBrMrR1V3LxiP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bc6qQkQbZBjJ04SrGh0So0"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 39}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fZeyso0tImKF9Ya+a7YD8"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 41}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_hoverSprite": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_pressedSprite": {"__uuid__": "e31108cd-1385-4e99-9d6e-4d0febb2e47d@f9941"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "75cc3b5e-91dc-454e-b74b-02fcfcf0dd08"}, "hoverSprite": {"__uuid__": "a6e7a164-911b-4bbc-9d38-a8c58bc98457"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3xo8Ps+pGL5NQJD36Pt/f"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 43}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13vY/G/XRCd4ka0tKC+6xX"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 45}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "efhut4/PpGIoKmMIYx2dhb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 47}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 314, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "78MswAYQRG25DlslyD5uyV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "93L7qp9z9CfYtWWQ51YyFh"}, {"__type__": "cc.Node", "_name": "Button_645", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 50}, {"__id__": 58}], "_active": true, "_components": [{"__id__": 66}, {"__id__": 68}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}], "_prefab": {"__id__": 76}, "_lpos": {"__type__": "cc.Vec3", "x": 189, "y": 270, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "ImageView_650", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 51}, {"__id__": 53}, {"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": -1, "y": -2.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 52}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9b41e348-e4dc-49a6-b71b-8b5bd04dc210@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9DUoi+QBKQ7g9nrxSmSS/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 54}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "33mPOmCu9P3qezrsY3NwEe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 56}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 304, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bNlLeZcZAg4ZU9gvbZR2f"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a20kMZT7BJ4oqNweTPrZKH"}, {"__type__": "cc.Node", "_name": "ImageView_653", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 59}, {"__id__": 61}, {"__id__": 63}], "_prefab": {"__id__": 65}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -0.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 60}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "539f1d6e-bb1c-4dfa-9985-5df1d0f01bf9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60BNKjm9xLo4/KO5hjMWqI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 62}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "98XALxUL5KSKCaiJgU7FiC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 64}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "22sESGGR1PoY/kt65n1Ryb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3fq0HR9KpBfJnetUJilZW9"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 67}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97SzD+hYtDtajkgubkXW7C"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 69}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_hoverSprite": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_pressedSprite": {"__uuid__": "e31108cd-1385-4e99-9d6e-4d0febb2e47d@f9941"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "75cc3b5e-91dc-454e-b74b-02fcfcf0dd08"}, "hoverSprite": {"__uuid__": "a6e7a164-911b-4bbc-9d38-a8c58bc98457"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34BKjYx4RISZIhNNjwyUFU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 71}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfVM2T+hNDYLeu30xic9XD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 73}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8WcfQ7O5Opa3fjwB2cAzl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 75}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 314, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2zOuAXL1KhbuhxsyH3MyZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0Ay2IucxPrpWKJvIztJ9E"}, {"__type__": "cc.Node", "_name": "Button_645_Copy0", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 78}, {"__id__": 86}], "_active": true, "_components": [{"__id__": 94}, {"__id__": 96}, {"__id__": 98}, {"__id__": 100}, {"__id__": 102}], "_prefab": {"__id__": 104}, "_lpos": {"__type__": "cc.Vec3", "x": 458, "y": 270, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "ImageView_650_Copy0", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 79}, {"__id__": 81}, {"__id__": 83}], "_prefab": {"__id__": 85}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 80}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9b41e348-e4dc-49a6-b71b-8b5bd04dc210@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50eVVOFhZEI4wSkX2LBLsN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 82}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "77NXcCHURIDYu2KJibvKXF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 84}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 304, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "57dLnf2HJORbGE5WAYTWF3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9qNOknh9B9b5zhZh+YZMo"}, {"__type__": "cc.Node", "_name": "ImageView_654", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [], "_active": true, "_components": [{"__id__": 87}, {"__id__": 89}, {"__id__": 91}], "_prefab": {"__id__": 93}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -0.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "__prefab": {"__id__": 88}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1662774f-d15e-4f87-92a2-fb51d5ad6aab@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01cB7BRmhK/6nLUM/WiqE+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "__prefab": {"__id__": 90}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5kNLtfK5GYpiNhAB2huds"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "__prefab": {"__id__": 92}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 122, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "78NoI8Ni5OjZhMoqHBXPit"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bwREqI89M46qDmQ0gfLtm"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 95}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33Ik7TYjxMPbBpsF2SFhBD"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 97}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_hoverSprite": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_pressedSprite": {"__uuid__": "e31108cd-1385-4e99-9d6e-4d0febb2e47d@f9941"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "75cc3b5e-91dc-454e-b74b-02fcfcf0dd08"}, "hoverSprite": {"__uuid__": "a6e7a164-911b-4bbc-9d38-a8c58bc98457"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42e7MdWdJOia5zqTKK8kao"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 99}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "94733959-cccc-4036-819d-6b4fda047a9c@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86ZDrHkHdDwqgMjOiXgUHD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 101}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5DgvUcQhOjYcEoks/4FKt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 103}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 314, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2a5/0QSnNMooySFFewVYrx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dagpCcnpJd6lWqgG+hrDh"}, {"__type__": "cc.Node", "_name": "ImageView_655", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 106}, {"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": 324, "y": 609, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7969, "y": 0.4997, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "__prefab": {"__id__": 107}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "03e53b05-f0ea-4976-a4cb-6a7c3a6f8d03@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cdi+K4KlBubBUtBtWjipy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "__prefab": {"__id__": 109}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "09rzpTvsNLaopeIwhyJcyG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "__prefab": {"__id__": 111}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 653, "height": 6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "06HAaYzlhB06eSjBeHImbz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "056OFChXZMm5KVPjNyWkh9"}, {"__type__": "cc.Node", "_name": "ImageView_655_Copy0", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 114}, {"__id__": 116}, {"__id__": 118}], "_prefab": {"__id__": 120}, "_lpos": {"__type__": "cc.Vec3", "x": 317, "y": 474, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7969, "y": 0.4997, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 115}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "03e53b05-f0ea-4976-a4cb-6a7c3a6f8d03@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bMXOtMl9AzoRFYw0i5tLV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 117}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "82Ov7XB3pOC4ET/7zmsj6v"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 119}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 653, "height": 6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "82xeMsJMNAgJdrD1EOqhf7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fk3P8E4FGG5655OBm+JRl"}, {"__type__": "cc.Node", "_name": "ImageView_844", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 122}, {"__id__": 124}, {"__id__": 126}], "_prefab": {"__id__": 128}, "_lpos": {"__type__": "cc.Vec3", "x": 159.2, "y": 644, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 123}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "64c670e8-5083-488f-914a-e74328893b37@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebrZztyfhGRp9Uoi+XUb+k"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 125}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cfpzMd9dIf5bBXBLSDrFC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 127}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2lO3ncA9F1YCNwScFNwfb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81D014U+NEr7NFo7IQLBsu"}, {"__type__": "cc.Node", "_name": "ImageView_1032", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 130}, {"__id__": 132}, {"__id__": 134}], "_prefab": {"__id__": 136}, "_lpos": {"__type__": "cc.Vec3", "x": 289, "y": 632.1161, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7123, "y": 0.7123, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 131}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5736d0ea-3c4f-4379-a4a8-9b5e8bee6c39@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe49LiR3RNcIDAzsQIBjC+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 133}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "82dNWXFC9KGYcIJ7+D4gGg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 135}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ca4WUAaFVOKZOmsHyCFd0N"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31wC4WU3FMcbGunOvK/urb"}, {"__type__": "cc.Node", "_name": "ImageView_1034", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 138}, {"__id__": 140}, {"__id__": 142}], "_prefab": {"__id__": 144}, "_lpos": {"__type__": "cc.Vec3", "x": 109.6, "y": 568.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 139}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "885a5406-13db-4547-944a-fefe41cc7f23@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36AoHQmgdKTb/fyOYgnTx5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 141}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dnYLBKI1CyoDGiSye3ad3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 143}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "96VzXd7udHzaIwpCa89VXO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6b3QyeoU9MDLwWclLGvlEn"}, {"__type__": "cc.Node", "_name": "ImageView_1035", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 146}, {"__id__": 148}, {"__id__": 150}], "_prefab": {"__id__": 152}, "_lpos": {"__type__": "cc.Vec3", "x": 110, "y": 512.4001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 147}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "eb285779-b5f4-4177-8068-801c3e7b625f@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7u0KKaCNO+bUHumCBWgb6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 149}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5b+U3jsdNGPLGNaAm2De8U"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 151}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "967UcMwUFLdJXaII3Kwn0l"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34hA1aKuZADbrz920ZXv1Z"}, {"__type__": "cc.Node", "_name": "ImageView_1036", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 154}, {"__id__": 156}, {"__id__": 158}], "_prefab": {"__id__": 160}, "_lpos": {"__type__": "cc.Vec3", "x": 117.7, "y": 429, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 155}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "de5b231e-c72f-470b-bb7a-0c3d39736e60@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73e6ACO4BLXa9KChV3yqfa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 157}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cC+UfZoNG6ombk9LOwwOa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 159}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fTTjIr3lKTKXQ9PxbRaLI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7Rd+iLA1Nbo2MvCUjnV8M"}, {"__type__": "cc.Node", "_name": "ImageView_1737", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 162}, {"__id__": 164}, {"__id__": 166}], "_prefab": {"__id__": 168}, "_lpos": {"__type__": "cc.Vec3", "x": 293, "y": 501, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 163}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "387ad89b-1402-447d-8442-ef08a2490cc3@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aayy8PrRPkr3UN/Oo37Xo"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 165}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eMuyJFipMkq3p01daJaaP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 167}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 134}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dz3Vhhl5Lnp81P86NcTgL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "abJHsmdhtENYjpwdqTRNvC"}, {"__type__": "cc.Node", "_name": "ImageView_1738", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 170}, {"__id__": 172}, {"__id__": 174}], "_prefab": {"__id__": 176}, "_lpos": {"__type__": "cc.Vec3", "x": 293, "y": 475, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 169}, "_enabled": true, "__prefab": {"__id__": 171}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "747c945e-6c6a-499e-866b-b7220184defd@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3FRDQk9hMP5SjGN3QNWm7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 169}, "_enabled": true, "__prefab": {"__id__": 173}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbNPzHdqdDp6tqOtaNA8bL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 169}, "_enabled": true, "__prefab": {"__id__": 175}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aR/0tDixMGJ7WLu2CIbD5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cf0i4vK3pLZIJWxOEgKFXb"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 178}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30rh+MgOFAJrKtgeHKVqqZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 180}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0ARs7hs5Gx43DyuogECsg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 182}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ea7IuiY+FD/7XicXlkQpDx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcfbS5BoNJHJ7XxciDNVrT"}, {"__type__": "cc.Node", "_name": "Text_1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 185}, {"__id__": 187}, {"__id__": 189}], "_prefab": {"__id__": 191}, "_lpos": {"__type__": "cc.Vec3", "x": 498.5674, "y": 644, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 184}, "_enabled": true, "__prefab": {"__id__": 186}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "1000", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "2768b951-ce1e-4ed2-acac-6ae1dfe4129e"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8d9XSIfIFIp5QiWAUZLLZ/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 184}, "_enabled": true, "__prefab": {"__id__": 188}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "97DohFrAVJ7bh7SnzkjAye"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 184}, "_enabled": true, "__prefab": {"__id__": 190}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "25TSc565BDiZazxIH4nHFd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4xKjG1n5Eh5Z2U11miJty"}, {"__type__": "cc.Node", "_name": "Text_1_0", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 193}, {"__id__": 195}, {"__id__": 197}], "_prefab": {"__id__": 199}, "_lpos": {"__type__": "cc.Vec3", "x": 498.5674, "y": 568.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "__prefab": {"__id__": 194}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "1000", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "2768b951-ce1e-4ed2-acac-6ae1dfe4129e"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52f2S9mnJPZJyK411P4GF+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "__prefab": {"__id__": 196}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccOTg9/aZNyIr6ybefilDH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "__prefab": {"__id__": 198}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "af32U2uONKJrDz660OHkaS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efv/YI9N1GFIkDCCs26Uvi"}, {"__type__": "cc.Node", "_name": "Text_1_1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 201}, {"__id__": 203}, {"__id__": 205}], "_prefab": {"__id__": 207}, "_lpos": {"__type__": "cc.Vec3", "x": 498.5674, "y": 512.4, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 202}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "1000", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "2768b951-ce1e-4ed2-acac-6ae1dfe4129e"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "660MxQe7FELJjG8bRMcTSj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 204}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "72d+g1xP1FjJMHt2WGvRuh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 206}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "df37Tq/ktPxq7PtAH6urxV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90VouDGhFDmYU0ni9Yxi6J"}, {"__type__": "cc.Node", "_name": "Text_1_2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 209}, {"__id__": 211}, {"__id__": 213}], "_prefab": {"__id__": 215}, "_lpos": {"__type__": "cc.Vec3", "x": 498.5675, "y": 429.0001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 210}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "50000000", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "2768b951-ce1e-4ed2-acac-6ae1dfe4129e"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9VBYC4EhDWY2IhJlkns9s"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 212}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f58WOqr/tFjqqxQWtcs+9W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 214}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "477XThYXlAKbD1dytIFhJk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23p4wyKNpBMrapSSlMXPGr"}, {"__type__": "cc.Node", "_name": "AtlasLabel_1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 217}, {"__id__": 219}, {"__id__": 221}], "_prefab": {"__id__": 223}, "_lpos": {"__type__": "cc.Vec3", "x": 320.4431, "y": 633.1251, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.625, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "_type": 2, "_checkNormalBackFrame": null, "_checkPressedBackFrame": null, "_checkDisableBackFrame": null, "_checkNormalFrame": null, "_checkDisableFrame": null, "checkInteractable": true, "isChecked": true, "_atlasFrame": {"__uuid__": "7abe041c-e936-41c5-9c43-8968a54cc0da"}, "firstChar": ".", "charWidth": 37, "charHeight": 34, "string": "2", "_sliderBackFrame": null, "_sliderBarFrame": null, "_sliderBtnNormalFrame": null, "_sliderBtnPressedFrame": null, "_sliderBtnDisabledFrame": null, "sliderInteractable": true, "sliderProgress": 0.5, "listInertia": true, "listDirection": 0, "listHorizontalAlign": 0, "listVerticalAlign": 0, "listPadding": 0, "_id": "", "__prefab": {"__id__": 218}}, {"__type__": "cc.CompPrefabInfo", "fileId": "60GAfu8ClLy4JhmauC+ex8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 220}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "57PokdTwJClLUcqx2ItJs3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 222}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 37, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1GYIxkMtOzpBWS3hW5tRa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0IU0oxqZLToIgHJLTEhin"}, {"__type__": "cc.Node", "_name": "ImageView_1353", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 225}, {"__id__": 227}, {"__id__": 229}], "_prefab": {"__id__": 231}, "_lpos": {"__type__": "cc.Vec3", "x": 437.1659, "y": 456.7208, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5486, "y": 0.5486, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 226}, "_customMaterial": null, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5725ecad-dc31-4990-a2e0-69c6a4c81a3e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18RS6csGFHI5BB3aZAHg7m"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 228}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbyhTGNDhICZFNT0nJoQyH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 230}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 111, "height": 59}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4MUBSkahJbKM5TfATEStq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c404ZBJV5McqFuuax8THpb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 233}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8RAg6aHVDrqQZmrK9He5x"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 235}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5c8bJ147xLMY2aGO/KpK0g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25MiNShbJKS6WwgIDEfeHa"}]