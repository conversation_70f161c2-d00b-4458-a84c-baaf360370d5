{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "aac3ea2d-40d8-42d5-88ed-dbb183387a99", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "aac3ea2d-40d8-42d5-88ed-dbb183387a99@6c48a", "displayName": "zadan", "id": "6c48a", "name": "texture", "userData": {"minfilter": "linear", "magfilter": "linear", "wrapModeT": "clamp-to-edge", "wrapModeS": "clamp-to-edge", "mipfilter": "none", "imageUuidOrDatabaseUri": "aac3ea2d-40d8-42d5-88ed-dbb183387a99", "isUuid": true, "visible": false, "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "aac3ea2d-40d8-42d5-88ed-dbb183387a99@f9941", "displayName": "zadan", "id": "f9941", "name": "spriteFrame", "userData": {"importer": "sprite-frame", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 78, "height": 32, "rawWidth": 78, "rawHeight": 32, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "imageUuidOrDatabaseUri": "aac3ea2d-40d8-42d5-88ed-dbb183387a99@6c48a", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-39, -16, 0, 39, -16, 0, -39, 16, 0, 39, 16, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 32, 78, 32, 0, 0, 78, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-39, -16, 0], "maxPos": [39, 16, 0]}, "isUuid": true, "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "redirect": "aac3ea2d-40d8-42d5-88ed-dbb183387a99@6c48a", "hasAlpha": true}}